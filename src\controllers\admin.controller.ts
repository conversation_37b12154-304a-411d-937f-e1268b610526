import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import User from '../models/User';

// Get all brand owners along with info
export const getAllBrandOwners = async (req: Request, res: Response) => {
  try {
    const brandOwners = await User.find({ role: 'brandOwner' })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand owners retrieved successfully',
      count: brandOwners.length,
      brandOwners: brandOwners.map(owner => ({
        id: owner._id,
        email: owner.email,
        brandName: owner.brandName,
        address: owner.address,
        websitelink: owner.websitelink,
        bio: owner.bio,
        phoneNumber: owner.phoneNumber,
        ProfilePicture: owner.ProfilePicture,
        isEmailVerified: owner.isEmailVerified,
        isBrandOwnerVerified: owner.isBrandOwnerVerified,
        createdAt: owner.createdAt,
        updatedAt: owner.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all brand owners:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand owners',
      error
    });
  }
};

// Get all brands whose isBrandOwnerVerified is false
export const getUnverifiedBrands = async (req: Request, res: Response) => {
  try {
    const unverifiedBrands = await User.find({
      role: 'brandOwner',
      isBrandOwnerVerified: false
    })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Unverified brands retrieved successfully',
      count: unverifiedBrands.length,
      unverifiedBrands: unverifiedBrands.map(brand => ({
        id: brand._id,
        email: brand.email,
        brandName: brand.brandName,
        address: brand.address,
        websitelink: brand.websitelink,
        bio: brand.bio,
        phoneNumber: brand.phoneNumber,
        ProfilePicture: brand.ProfilePicture,
        isEmailVerified: brand.isEmailVerified,
        isBrandOwnerVerified: brand.isBrandOwnerVerified,
        createdAt: brand.createdAt,
        updatedAt: brand.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting unverified brands:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving unverified brands',
      error
    });
  }
};

// Toggle brand owner verification status
export const toggleBrandOwnerVerification = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId);
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a Brand owner'
      });
    }

    // Toggle verification status
    const newVerificationStatus = !brandOwner.isBrandOwnerVerified;

    const updatedBrandOwner = await User.findByIdAndUpdate(
      brandOwnerId,
      { isBrandOwnerVerified: newVerificationStatus },
      { new: true }
    ).select('-password -otp -otpCreatedAt');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: `Brand owner verification status ${newVerificationStatus ? 'enabled' : 'disabled'} successfully`,
      brandOwner: {
        id: updatedBrandOwner?._id,
        email: updatedBrandOwner?.email,
        brandName: updatedBrandOwner?.brandName,
        isBrandOwnerVerified: updatedBrandOwner?.isBrandOwnerVerified
      }
    });

  } catch (error) {
    console.error('Error toggling brand owner verification:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating brand owner verification status',
      error
    });
  }
};

// Get all info of a brand (all info used to signup)
export const getBrandDetails = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId).select('-password -otp -otpCreatedAt');
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a business owner'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand details retrieved successfully',
      brand: {
        id: brandOwner._id,
        email: brandOwner.email,
        brandName: brandOwner.brandName,
        phoneNumber: brandOwner.phoneNumber,
        address: brandOwner.address,
        websitelink: brandOwner.websitelink,
        bio: brandOwner.bio,
        ProfilePicture: brandOwner.ProfilePicture,
        role: brandOwner.role,
        isEmailVerified: brandOwner.isEmailVerified,
        isBrandOwnerVerified: brandOwner.isBrandOwnerVerified,
        createdAt: brandOwner.createdAt,
        updatedAt: brandOwner.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting brand details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand details',
      error
    });
  }
};

// Get all users
export const getAllUsers = async (req: Request, res: Response) => {
  try {
    const users = await User.find({ role: 'user' })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Users retrieved successfully',
      count: users.length,
      users: users.map(user => ({
        id: user._id,
        email: user.email,
        fullname: user.fullname,
        phoneNumber: user.phoneNumber,
        ProfilePicture: user.ProfilePicture,
        address: user.address,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all users:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving users',
      error
    });
  }
};