import { Router } from 'express';
import {
  createSubCategory,
  getAllSubCategories,
  getSubCategoryDetails,
  updateSubCategory,
  deleteSubCategory
} from '../controllers/subcategory.controller';
import { verifyTokenForAdmin } from '../middlewares/auth.middlewares';

const router = Router();

// Public routes - get subcategories (no authentication required)
router.get('/', getAllSubCategories);
router.get('/:subCategoryId', getSubCategoryDetails);
router.get('/category/:categoryId', getAllSubCategories);

// Protected routes - require authentication
router.post('/', verifyTokenForAdmin, createSubCategory);

router.put('/:subCategoryId', verifyTokenForAdmin, updateSubCategory);

router.delete('/:subCategoryId', verifyTokenForAdmin, deleteSubCategory);


export default router;
