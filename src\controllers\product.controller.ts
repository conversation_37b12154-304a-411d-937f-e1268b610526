import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import Product from '../models/Product';
import Shop from '../models/Shop';
import Category from '../models/Category';
import SubCategory from '../models/SubCategory';
import { ICreateProductRequest, IUpdateProductRequest, IMarkAsSoldRequest } from '../interfaces/product.interfaces';
import { validateCreateProductData, validateUpdateProductData, validateMarkAsSoldData } from '../utils/validations';
import { uploadFile } from '../utils/mediaHandling';
import User from '../models/User';

// Create a new product
export const createProduct = async (req: Request, res: Response) => {
  try {
    const productData: ICreateProductRequest = req.body;
    const userId = req.user?._id;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Check if product media files are provided
    if (!files || !files.productMedia || files.productMedia.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'At least one product image is required'
      });
    }

    // Upload product media files to S3 and get URLs
    let productMediaUrls: string[] = [];
    try {
      for (const file of files.productMedia) {
        const url = await uploadFile(file);
        productMediaUrls.push(url);
      }
    } catch (uploadError) {
      console.error('Error uploading product media files:', uploadError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Error uploading product images'
      });
    }

    // Upload size chart image if provided
    let sizeChartImageUrl: string | undefined;
    if (files.sizeChartImage && files.sizeChartImage.length > 0) {
      try {
        sizeChartImageUrl = await uploadFile(files.sizeChartImage[0]);
      } catch (uploadError) {
        console.error('Error uploading size chart image:', uploadError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading size chart image'
        });
      }
    }

    // Add uploaded URLs to productData
    productData.productMedia = productMediaUrls;
    if (sizeChartImageUrl) {
      productData.sizeChartImage = sizeChartImageUrl;
    }

    // Validate input data (including uploaded media URLs)
    const validation = validateCreateProductData(productData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(productData.shop), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to add products to this shop'
      });
    }

    // Verify category exists
    const category = await Category.findById(new Types.ObjectId(productData.productCategory));
    if (!category) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Verify subcategory exists and belongs to the category
    const subCategory = await SubCategory.findOne({
      _id: new Types.ObjectId(productData.productSubCategory),
      category: new Types.ObjectId(productData.productCategory)
    });

    if (!subCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Subcategory not found or does not belong to the specified category'
      });
    }

    // Calculate discounted price if discount percentage is provided
    const price = Number(productData.price);
    let discountedPrice = price; // Default to original price
    let discountedPercentage = 0; // Default to no discount

    if (productData.discountedPercentage && Number(productData.discountedPercentage) > 0) {
      discountedPercentage = Number(productData.discountedPercentage);
      
      // Validate discount percentage
      if (discountedPercentage < 0 || discountedPercentage > 100) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Discount percentage must be between 0 and 100'
        });
      }

      // Calculate discounted price: price - (price * discountPercentage / 100)
      discountedPrice = price - (price * discountedPercentage / 100);
      
      // Round to 2 decimal places to avoid floating point precision issues
      discountedPrice = Math.round(discountedPrice * 100) / 100;
    }

    // Create new product
    const newProduct = new Product({
      productName: productData.productName.trim(),
      productDescription: productData.productDescription.trim(),
      productCategory: new Types.ObjectId(productData.productCategory),
      productSubCategory: new Types.ObjectId(productData.productSubCategory),
      shop: new Types.ObjectId(productData.shop),
      tags: productData.tags.map(tag => tag.trim()),
      productMedia: productData.productMedia,
      sizeChartImage: productData.sizeChartImage,
      price: price,
      discountedPercentage: discountedPercentage,
      discountedPrice: discountedPrice,
      sizes: productData.sizes,
      colors: productData.colors.map(color => color.trim()),
      stockQuantity: Number(productData.stockQuantity),
      stockSold: 0,
      isActive: true
    });

    await newProduct.save();

    // Update shop's product count
    await Shop.findByIdAndUpdate(
      new Types.ObjectId(productData.shop),
      { $inc: { noOfProducts: 1 } }
    );

    // Populate references for response
    await newProduct.populate([
      { path: 'productCategory', select: 'categoryName categoryType' },
      { path: 'productSubCategory', select: 'subCategoryName' },
      { path: 'shop', select: 'shopName' }
    ]);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Product created successfully',
      product: {
        id: newProduct._id,
        productName: newProduct.productName,
        productDescription: newProduct.productDescription,
        productCategory: newProduct.productCategory,
        productSubCategory: newProduct.productSubCategory,
        shop: newProduct.shop,
        tags: newProduct.tags,
        isActive: newProduct.isActive,
        productMedia: newProduct.productMedia,
        sizeChartImage: newProduct.sizeChartImage,
        price: newProduct.price,
        discountedPercentage: newProduct.discountedPercentage,
        discountedPrice: newProduct.discountedPrice,
        sizes: newProduct.sizes,
        colors: newProduct.colors,
        stockQuantity: newProduct.stockQuantity,
        stockSold: newProduct.stockSold,
        isLowStock: newProduct.isLowStock,
        createdAt: newProduct.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products of a specific shop (updated to include sizeChartImage)

export const getShopProducts = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to view products of this shop'
      });
    }

    const products = await Product.find({ shop: new Types.ObjectId(shopId) })
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName')
      .sort({ createdAt: -1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop products retrieved successfully',
      shop: {
        id: shop._id,
        shopName: shop.shopName,
        noOfProducts: shop.noOfProducts
      },
      count: products.length,
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting shop products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shop products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get shop details with product count
export const getShopDetailsWithProducts = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to view this shop'
      });
    }

    // Get product count and low stock count
    const totalProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId) });
    const activeProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId), isActive: true });
    const lowStockProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId), isLowStock: true });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop details retrieved successfully',
      shop: {
        id: shop._id,
        shopName: shop.shopName,
        shopImage: shop.shopImage,
        noOfProducts: shop.noOfProducts,
        totalProducts,
        activeProducts,
        lowStockProducts,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting shop details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shop details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get specific product details (updated to include sizeChartImage)
export const getProductDetails = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('productCategory', 'categoryName categoryType categoryImage')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .select('-__v');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product details retrieved successfully',
      product: {
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting product details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving product details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


// Get remaining stock quantity of a specific product
export const getProductStock = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    const product = await Product.findById(new Types.ObjectId(productId))
      .select('productName stockQuantity stockSold isLowStock');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product stock information retrieved successfully',
      stock: {
        productId: product._id,
        productName: product.productName,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        remainingStock: product.stockQuantity,
        isLowStock: product.isLowStock
      }
    });

  } catch (error) {
    console.error('Error getting product stock:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving product stock',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark product as sold (deduct stock)
export const markAsSold = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { quantity }: IMarkAsSoldRequest = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // Validate input data
    const validation = validateMarkAsSoldData({ quantity });
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to update this product'
      });
    }

    // Check if enough stock is available
    if (product.stockQuantity < quantity) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: `Insufficient stock. Available: ${product.stockQuantity}, Requested: ${quantity}`
      });
    }

    // Update stock quantities
    product.stockQuantity -= quantity;
    product.stockSold += quantity;
    product.isLowStock = product.stockQuantity < 50;

    await product.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product marked as sold successfully',
      product: {
        id: product._id,
        productName: product.productName,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        quantitySold: quantity
      }
    });

  } catch (error) {
    console.error('Error marking product as sold:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error marking product as sold',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


// Update product
export const updateProduct = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const updateData: IUpdateProductRequest = req.body;
    const userId = req.user?._id;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // If product media files are provided, upload them to S3 and get URLs
    if (files && files.productMedia && files.productMedia.length > 0) {
      let productMediaUrls: string[] = [];
      try {
        for (const file of files.productMedia) {
          const url = await uploadFile(file);
          productMediaUrls.push(url);
        }
        if (updateData.productMedia && updateData.productMedia.length > 0) {
          console.log("updateData.productMedia before", updateData.productMedia);
          updateData.productMedia.push(...productMediaUrls);
        } else {
          updateData.productMedia = productMediaUrls;
        }
      } catch (uploadError) {
        console.error('Error uploading product media files:', uploadError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading product images'
        });
      }
    }

    // If size chart image is provided, upload it to S3 and get URL
    if (files && files.sizeChartImage && files.sizeChartImage.length > 0) {
      try {
        const sizeChartImageUrl = await uploadFile(files.sizeChartImage[0]);
        updateData.sizeChartImage = sizeChartImageUrl;
      } catch (uploadError) {
        console.error('Error uploading size chart image:', uploadError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading size chart image'
        });
      }
    }

    // Validate input data
    const validation = validateUpdateProductData(updateData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to update this product'
      });
    }

    // If category is being updated, verify it exists
    if (updateData.productCategory) {
      const category = await Category.findById(new Types.ObjectId(updateData.productCategory));
      if (!category) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Category not found'
        });
      }
    }

    // If subcategory is being updated, verify it exists and belongs to the category
    if (updateData.productSubCategory) {
      const targetCategoryId = updateData.productCategory ?
        new Types.ObjectId(updateData.productCategory) :
        product.productCategory;

      const subCategory = await SubCategory.findOne({
        _id: new Types.ObjectId(updateData.productSubCategory),
        category: targetCategoryId
      });

      if (!subCategory) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Subcategory not found or does not belong to the specified category'
        });
      }
    }

    // Validate discount percentage if provided
    if (updateData.discountedPercentage !== undefined) {
      const discountPercentage = Number(updateData.discountedPercentage);
      if (discountPercentage < 0 || discountPercentage > 100) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Discount percentage must be between 0 and 100'
        });
      }
    }

    // Calculate discounted price based on price and discount percentage changes
    let calculatedDiscountedPrice: number | undefined;
    const newPrice = updateData.price !== undefined ? Number(updateData.price) : product.price;
    const newDiscountPercentage = updateData.discountedPercentage !== undefined ? 
      Number(updateData.discountedPercentage) : product.discountedPercentage;

    // Recalculate discounted price if either price or discount percentage is being updated
    if (updateData.price !== undefined || updateData.discountedPercentage !== undefined) {
      if (newDiscountPercentage && newDiscountPercentage > 0) {
        calculatedDiscountedPrice = newPrice - (newPrice * newDiscountPercentage / 100);
        calculatedDiscountedPrice = Math.round(calculatedDiscountedPrice * 100) / 100;
      } else {
        calculatedDiscountedPrice = newPrice; // No discount, discounted price equals original price
      }
    }

    // Prepare update object
    const updateObject: any = {};
    if (updateData.productName) updateObject.productName = updateData.productName.trim();
    if (updateData.productDescription) updateObject.productDescription = updateData.productDescription.trim();
    if (updateData.productCategory) updateObject.productCategory = new Types.ObjectId(updateData.productCategory);
    if (updateData.productSubCategory) updateObject.productSubCategory = new Types.ObjectId(updateData.productSubCategory);
    if (updateData.tags) updateObject.tags = updateData.tags.map(tag => tag.trim());
    if (updateData.isActive !== undefined) updateObject.isActive = updateData.isActive;
    if (updateData.productMedia) updateObject.productMedia = updateData.productMedia;
    if (updateData.sizeChartImage !== undefined) updateObject.sizeChartImage = updateData.sizeChartImage;
    if (updateData.price !== undefined) updateObject.price = updateData.price;
    if (updateData.discountedPercentage !== undefined) updateObject.discountedPercentage = updateData.discountedPercentage;
    if (calculatedDiscountedPrice !== undefined) updateObject.discountedPrice = calculatedDiscountedPrice;
    if (updateData.sizes) updateObject.sizes = updateData.sizes;
    if (updateData.colors) updateObject.colors = updateData.colors.map(color => color.trim());
    if (updateData.stockQuantity !== undefined) {
      updateObject.stockQuantity = updateData.stockQuantity;
      updateObject.isLowStock = updateData.stockQuantity < 50;
    }

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      new Types.ObjectId(productId),
      updateObject,
      { new: true, runValidators: true }
    ).populate([
      { path: 'productCategory', select: 'categoryName categoryType' },
      { path: 'productSubCategory', select: 'subCategoryName' },
      { path: 'shop', select: 'shopName' }
    ]);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product updated successfully',
      product: {
        id: updatedProduct!._id,
        productName: updatedProduct!.productName,
        productDescription: updatedProduct!.productDescription,
        productCategory: updatedProduct!.productCategory,
        productSubCategory: updatedProduct!.productSubCategory,
        shop: updatedProduct!.shop,
        tags: updatedProduct!.tags,
        isActive: updatedProduct!.isActive,
        productMedia: updatedProduct!.productMedia,
        sizeChartImage: updatedProduct!.sizeChartImage,
        price: updatedProduct!.price,
        discountedPercentage: updatedProduct!.discountedPercentage,
        discountedPrice: updatedProduct!.discountedPrice,
        sizes: updatedProduct!.sizes,
        colors: updatedProduct!.colors,
        stockQuantity: updatedProduct!.stockQuantity,
        stockSold: updatedProduct!.stockSold,
        isLowStock: updatedProduct!.isLowStock,
        updatedAt: updatedProduct!.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete product
export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId shopName');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to delete this product'
      });
    }

    // Delete product
    await Product.findByIdAndDelete(new Types.ObjectId(productId));

    // Update shop's product count
    await Shop.findByIdAndUpdate(
      product.shop,
      { $inc: { noOfProducts: -1 } }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product deleted successfully',
      deletedProduct: {
        id: product._id,
        productName: product.productName,
        shop: shop.shopName
      }
    });

  } catch (error) {
    console.error('Error deleting product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products for logged in brand owner (from all their shops)
export const getAllProducts = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    
    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const {
      categoryId,
      subCategoryId,
      isActive,
      isLowStock,
      minPrice,
      maxPrice,
      search,
      page = 1,
      limit = 10
    } = req.query;

    // First, get all shops owned by this brand owner
    const userShops = await Shop.find({ userId: new Types.ObjectId(userId) }).select('_id');
    const shopIds = userShops.map(shop => shop._id);

    if (shopIds.length === 0) {
      return res.status(StatusCodes.OK).json({
        success: true,
        message: 'No shops found for this brand owner',
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalProducts: 0,
          hasNextPage: false,
          hasPrevPage: false
        },
        products: []
      });
    }

    // Build filter object
    const filter: any = {
      shop: { $in: shopIds } // Only products from user's shops
    };

    if (categoryId && Types.ObjectId.isValid(categoryId as string)) {
      filter.productCategory = new Types.ObjectId(categoryId as string);
    }

    if (subCategoryId && Types.ObjectId.isValid(subCategoryId as string)) {
      filter.productSubCategory = new Types.ObjectId(subCategoryId as string);
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    if (isLowStock !== undefined) {
      filter.isLowStock = isLowStock === 'true';
    }

    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = parseFloat(minPrice as string);
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice as string);
    }

    if (search) {
      filter.$text = { $search: search as string };
    }

    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 10;
    const skip = (pageNum - 1) * limitNum;

    const products = await Product.find(filter)
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .select('-__v');

    const totalProducts = await Product.countDocuments(filter);
    const totalPages = Math.ceil(totalProducts / limitNum);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Products retrieved successfully',
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalProducts,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      },
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin endpoint to get all products from all shops
export const getAllProductsAdmin = async (req: Request, res: Response) => {
  try {
    const {
      categoryId,
      subCategoryId,
      shopId,
      isActive,
      isLowStock,
      minPrice,
      maxPrice,
      search,
      page = 1,
      limit = 10
    } = req.query;

    // Build filter object
    const filter: any = {};

    if (categoryId && Types.ObjectId.isValid(categoryId as string)) {
      filter.productCategory = new Types.ObjectId(categoryId as string);
    }

    if (subCategoryId && Types.ObjectId.isValid(subCategoryId as string)) {
      filter.productSubCategory = new Types.ObjectId(subCategoryId as string);
    }

    if (shopId && Types.ObjectId.isValid(shopId as string)) {
      filter.shop = new Types.ObjectId(shopId as string);
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    if (isLowStock !== undefined) {
      filter.isLowStock = isLowStock === 'true';
    }

    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = parseFloat(minPrice as string);
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice as string);
    }

    if (search) {
      filter.$text = { $search: search as string };
    }

    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 10;
    const skip = (pageNum - 1) * limitNum;

    const products = await Product.find(filter)
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .select('-__v');

    const totalProducts = await Product.countDocuments(filter);
    const totalPages = Math.ceil(totalProducts / limitNum);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'All products retrieved successfully',
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalProducts,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      },
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving all products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products by category with pagination (public)
export const getAllProductsByCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.body;
    
    // Get pagination parameters from query string
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const sortBy = (req.query.sortBy as string) || 'createdAt';
    const sortOrder = (req.query.sortOrder as string) === 'asc' ? 1 : -1;
    
    // Validate pagination parameters
    if (page < 1) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Page number must be greater than 0'
      });
    }
    
    if (limit < 1 || limit > 100) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Limit must be between 1 and 100'
      });
    }

    if (!categoryId || !Types.ObjectId.isValid(categoryId as string)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    const isExist = await Category.findById(new Types.ObjectId(categoryId as string));
    if (!isExist) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    console.log('Category found:', isExist);

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination metadata
    const totalProducts = await Product.countDocuments({ 
      productCategory: new Types.ObjectId(categoryId as string) 
    });

    // Create sort object
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder;

    const products = await Product.find({ productCategory: new Types.ObjectId(categoryId as string) })
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .select('-__v');

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Products retrieved successfully',
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalProducts,
        hasNextPage,
        hasPrevPage,
        limit,
        skip
      }
    });

  } catch (error) {
    console.error('Error getting products by category:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving products by category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products by sub category with pagination (public)
export const getAllProductsBySubCategory = async (req: Request, res: Response) => {
  try {
    const { subCategoryId } = req.query;
    
    // Get pagination parameters from query string
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const sortBy = (req.query.sortBy as string) || 'createdAt';
    const sortOrder = (req.query.sortOrder as string) === 'asc' ? 1 : -1;
    
    // Validate pagination parameters
    if (page < 1) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Page number must be greater than 0'
      });
    }
    
    if (limit < 1 || limit > 100) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Limit must be between 1 and 100'
      });
    }

    if (!subCategoryId || !Types.ObjectId.isValid(subCategoryId as string)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid sub category ID'
      });
    }

    const isExist = await SubCategory.findById(new Types.ObjectId(subCategoryId as string));
    if (!isExist) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Sub category not found'
      });
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination metadata
    const totalProducts = await Product.countDocuments({ 
      productSubCategory: new Types.ObjectId(subCategoryId as string) 
    });

    // Create sort object
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder;

    const products = await Product.find({ productSubCategory: new Types.ObjectId(subCategoryId as string) })
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .select('-__v');

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Products retrieved successfully',
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        sizeChartImage: product.sizeChartImage,
        price: product.price,
        discountedPercentage: product.discountedPercentage,
        discountedPrice: product.discountedPrice,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalProducts,
        hasNextPage,
        hasPrevPage,
        limit,
        skip
      }
    });

  } catch (error) {
    console.error('Error getting products by sub category:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving products by sub category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
