import { Request, Response } from 'express';
import Cart from '../models/Cart';
import Order from '../models/Order';
import Product from '../models/Product';
import User from '../models/User';
import DeliveryAddress from '../models/DeliveryAddress';
import { ICheckoutRequest, IApplyPromoCodeRequest, ICheckoutSummary } from '../interfaces/payment.interfaces';
import { IOrderItem, OrderStatus, PaymentStatus } from '../interfaces/order.interfaces';
import { Types } from 'mongoose';

export const applyPromoCode = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { promoCode }: IApplyPromoCodeRequest = req.body;

      if (!promoCode) {
        return res.status(400).json({
          success: false,
          message: 'Promo code is required'
        });
      }

      // Get user's cart
      const cart = await Cart.findOne({ user: userId }).populate('items.product');
      if (!cart || cart.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Cart is empty'
        });
      }

      // TODO: Implement actual promo code validation
      // For now, we'll use some sample promo codes
      let discountAmount = 0;
      let discountType = 'fixed';
      let isValid = false;

      const samplePromoCodes = {
        'SAVE10': { type: 'fixed', value: 10, minAmount: 50 },
        'DISCOUNT20': { type: 'percentage', value: 20, minAmount: 100 },
        'WELCOME15': { type: 'percentage', value: 15, minAmount: 0 },
        'FREESHIP': { type: 'shipping', value: 0, minAmount: 0 }
      };

      const promoData = samplePromoCodes[promoCode as keyof typeof samplePromoCodes];
      
      if (promoData && cart.totalAmount >= promoData.minAmount) {
        isValid = true;
        if (promoData.type === 'fixed') {
          discountAmount = promoData.value;
        } else if (promoData.type === 'percentage') {
          discountAmount = (cart.totalAmount * promoData.value) / 100;
        }
      }

      if (!isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid promo code or minimum order amount not met'
        });
      }

      // Calculate checkout summary with discount
      const shippingCost = promoCode === 'FREESHIP' ? 0 : (cart.totalAmount > 100 ? 0 : 10);
      const tax = cart.totalAmount * 0.08; // 8% tax
      const totalAmount = cart.totalAmount + shippingCost + tax - discountAmount;

      // Save the applied promo code to the cart
      cart.appliedPromoCode = promoCode;
      cart.discountAmount = discountAmount;
      await cart.save();

      const checkoutSummary: ICheckoutSummary = {
        items: cart.items,
        subtotal: cart.totalAmount,
        shippingCost,
        tax,
        discountAmount,
        promoCode,
        totalAmount: Math.max(0, totalAmount)
      };

      res.status(200).json({
        success: true,
        message: 'Promo code applied successfully',
        data: checkoutSummary
      });

    } catch (error: any) {
      console.error('Apply promo code error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to apply promo code',
        error: error.message
      });
    }
  };

export const removePromoCode = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;

      // Get user's cart
      const cart = await Cart.findOne({ user: userId }).populate('items.product');
      if (!cart || cart.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Cart is empty'
        });
      }

      // Clear the applied promo code
      cart.appliedPromoCode = undefined;
      cart.discountAmount = 0;
      await cart.save();

      // Calculate checkout summary without discount
      const shippingCost = cart.totalAmount > 100 ? 0 : 10;
      const tax = cart.totalAmount * 0.08; // 8% tax
      const totalAmount = cart.totalAmount + shippingCost + tax;

      const checkoutSummary: ICheckoutSummary = {
        items: cart.items,
        subtotal: cart.totalAmount,
        shippingCost,
        tax,
        discountAmount: 0,
        promoCode: undefined,
        totalAmount
      };

      res.status(200).json({
        success: true,
        message: 'Promo code removed successfully',
        data: checkoutSummary
      });

    } catch (error: any) {
      console.error('Remove promo code error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to remove promo code',
        error: error.message
      });
    }
  };

export const getCheckoutSummary = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      let { promoCode } = req.query;

      // Get user's cart
      const cart = await Cart.findOne({ user: userId }).populate('items.product');
      if (!cart || cart.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Cart is empty'
        });
      }

      // Use stored promo code if no promo code is provided in query
      if (!promoCode && cart.appliedPromoCode) {
        promoCode = cart.appliedPromoCode;
      }

      // Check for unavailable items
      const unavailableItems: any[] = [];
      const availableItems = cart.items.filter(item => {
        const product = item.product as any;
        if (!product || !product.isActive || product.stockQuantity < item.quantity) {
          unavailableItems.push({
            productName: product?.productName || 'Unknown Product',
            reason: !product ? 'Product not found' : 
                   !product.isActive ? 'Product no longer available' : 
                   'Insufficient stock'
          });
          return false;
        }
        return true;
      });

      const subtotal = availableItems.reduce((sum, item) => 
        sum + (item.priceAtTime * item.quantity), 0
      );

      // Apply promo code if provided
      let discountAmount = 0;
      if (promoCode) {
        // Use the same logic as applyPromoCode
        const samplePromoCodes = {
          'SAVE10': { type: 'fixed', value: 10, minAmount: 50 },
          'DISCOUNT20': { type: 'percentage', value: 20, minAmount: 100 },
          'WELCOME15': { type: 'percentage', value: 15, minAmount: 0 }
        };

        const promoData = samplePromoCodes[promoCode as string as keyof typeof samplePromoCodes];
        if (promoData && subtotal >= promoData.minAmount) {
          if (promoData.type === 'fixed') {
            discountAmount = promoData.value;
          } else if (promoData.type === 'percentage') {
            discountAmount = (subtotal * promoData.value) / 100;
          }
        }
      }

      const shippingCost = promoCode === 'FREESHIP' ? 0 : (subtotal > 100 ? 0 : 10);
      const tax = subtotal * 0.08; // 8% tax
      const totalAmount = subtotal + shippingCost + tax - discountAmount;

      const checkoutSummary: ICheckoutSummary = {
        items: availableItems,
        subtotal,
        shippingCost,
        tax,
        discountAmount,
        promoCode: promoCode as string,
        totalAmount: Math.max(0, totalAmount),
        unavailableItems: unavailableItems.length > 0 ? unavailableItems : undefined
      };

      res.status(200).json({
        success: true,
        message: 'Checkout summary retrieved successfully',
        data: checkoutSummary
      });

    } catch (error: any) {
      console.error('Get checkout summary error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get checkout summary',
        error: error.message
      });
    }
  };

  export const processCheckout = async (req: Request, res: Response) => 
    {
    try {
      const userId = req.user!._id;
      let { shippingAddressId, paymentDetails, promoCode, notes }: ICheckoutRequest = req.body;

      // Validate required fields
      if (!paymentDetails || !paymentDetails.paymentMethod) {
        return res.status(400).json({
          success: false,
          message: 'Payment details are required'
        });
      }

      // Get user and find shipping address
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Find the shipping address - use provided ID or default address
      let shippingAddress;
      if (shippingAddressId) {
        // Use provided address
        shippingAddress = await DeliveryAddress.findOne({
          _id: shippingAddressId,
          user: userId
        }).populate('user', 'fullname phoneNumber');

        if (!shippingAddress) {
          return res.status(404).json({
            success: false,
            message: 'Specified shipping address not found'
          });
        }
      } else {
        // Use default address
        shippingAddress = await DeliveryAddress.findOne({
          user: userId,
          isDefault: true
        }).populate('user', 'fullname phoneNumber');

        if (!shippingAddress) {
          return res.status(404).json({
            success: false,
            message: 'No default shipping address found. Please add an address first.'
          });
        }
      }

      // Get user's cart
      const cart = await Cart.findOne({ user: userId }).populate('items.product');
      if (!cart || cart.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Cart is empty'
        });
      }

      // Use stored promo code if no promo code is provided in request
      if (!promoCode && cart.appliedPromoCode) {
        promoCode = cart.appliedPromoCode;
      }

      // Validate cart items and check stock availability
      const orderItems: IOrderItem[] = [];
      let subtotal = 0;

      for (const cartItem of cart.items) {
        const product = cartItem.product as any;
        
        // Check if product still exists and is active
        if (!product || !product.isActive) {
          return res.status(400).json({
            success: false,
            message: `Product ${product?.productName || 'Unknown'} is no longer available`
          });
        }

        // Check stock availability
        if (product.stockQuantity < cartItem.quantity) {
          return res.status(400).json({
            success: false,
            message: `Only ${product.stockQuantity} items of ${product.productName} available in stock`
          });
        }

        const itemTotal = cartItem.priceAtTime * cartItem.quantity;
        
        orderItems.push({
          product: product._id,
          productName: product.productName,
          quantity: cartItem.quantity,
          size: cartItem.size,
          color: cartItem.color,
          priceAtTime: cartItem.priceAtTime,
          totalPrice: itemTotal
        });

        subtotal += itemTotal;
      }

      // Apply promo code discount
      let discountAmount = 0;
      if (promoCode) {
        const samplePromoCodes = {
          'SAVE10': { type: 'fixed', value: 10, minAmount: 50 },
          'DISCOUNT20': { type: 'percentage', value: 20, minAmount: 100 },
          'WELCOME15': { type: 'percentage', value: 15, minAmount: 0 }
        };

        const promoData = samplePromoCodes[promoCode as keyof typeof samplePromoCodes];
        if (promoData && subtotal >= promoData.minAmount) {
          if (promoData.type === 'fixed') {
            discountAmount = promoData.value;
          } else if (promoData.type === 'percentage') {
            discountAmount = (subtotal * promoData.value) / 100;
          }
        }
      }

      // Calculate costs
      const shippingCost = promoCode === 'FREESHIP' ? 0 : (subtotal > 100 ? 0 : 10);
      const tax = subtotal * 0.08; // 8% tax
      const totalAmount = subtotal + shippingCost + tax - discountAmount;

      // Generate unique order number
      let orderNumber: string;
      let isUnique = false;
      
      while (!isUnique) {
        orderNumber = Math.floor(10000 + Math.random() * 90000).toString();
        const existingOrder = await Order.findOne({ orderNumber });
        if (!existingOrder) {
          isUnique = true;
        }
      }

      // Create the order
      const order = new Order({
        user: userId,
        orderNumber: orderNumber!, // Explicitly set the order number
        items: orderItems,
        deliveryAddress: shippingAddress._id, // Reference to DeliveryAddress
        subtotal,
        shippingCost,
        tax,
        totalAmount: Math.max(0, totalAmount),
        orderStatus: OrderStatus.PENDING,
        paymentStatus: PaymentStatus.PENDING,
        promoCode,
        discountAmount,
        notes,
        paymentDetails: {
          paymentMethod: paymentDetails.paymentMethod,
          cardHolderName: paymentDetails.cardHolderName,
          // Note: In production, never store actual card details
          // cardNumber and cvv should be processed through payment gateway
        }
      });

      await order.save();

      // TODO: Process payment here
      // For now, we'll mark as paid for testing
      if (paymentDetails.paymentMethod === 'cash_on_delivery') {
        order.paymentStatus = PaymentStatus.PENDING;
      } else {
        // Simulate payment processing
        order.paymentStatus = PaymentStatus.PAID;
      }
      await order.save();

      // Update product stock quantities only after successful payment
      if (order.paymentStatus === PaymentStatus.PAID) {
        for (const cartItem of cart.items) {
          await Product.findByIdAndUpdate(
            cartItem.product,
            { 
              $inc: { 
                stockQuantity: -cartItem.quantity,
                stockSold: cartItem.quantity
              }
            }
          );
        }
      }

      // Clear the cart
      cart.items = [];
      cart.totalAmount = 0;
      cart.appliedPromoCode = undefined;
      cart.discountAmount = 0;
      await cart.save();

      // Populate order details for response
      await order.populate({
        path: 'items.product',
        select: 'productName productMedia'
      });

      res.status(201).json({
        success: true,
        message: 'Order placed successfully',
        data: {
          orderId: order._id,
          orderNumber: order.orderNumber,
          totalAmount: order.totalAmount,
          paymentStatus: order.paymentStatus,
          orderStatus: order.orderStatus
        }
      });

    } catch (error: any) {
      console.error('Process checkout error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process checkout',
        error: error.message
      });
    }
  };