import { Router } from 'express';
import {
  adminSignup,
  adminLogin,
  getAllBrandOwners,
  getUnverifiedBrands,
  toggleBrandOwnerVerification,
  getBrandDetails,
  getAllUsers
} from '../controllers/index';
import { getAllProductsAdmin } from '../controllers/product.controller';
import { verifyTokenForAdmin } from '../middlewares/auth.middlewares';

const router = Router();

// Public admin routes
router.post('/login', adminLogin);

// Protected admin routes - require admin authentication
router.use(verifyTokenForAdmin);

router.post('/signup', adminSignup);

// Product management
router.get('/products', getAllProductsAdmin);

// Brand owner management
router.get('/brand-owners', getAllBrandOwners);
router.get('/unverified-brands', getUnverifiedBrands);
router.put('/brand-owners/:brandOwnerId/toggle-verification', toggleBrandOwnerVerification);
router.get('/brand-owners/:brandOwnerId/details', getBrandDetails);

// User management
router.get('/users', getAllUsers);

export default router;
